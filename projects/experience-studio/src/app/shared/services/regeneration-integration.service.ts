import { Injectable, inject, DestroyRef } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { filter, tap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { SequentialRegenerationService } from './sequential-regeneration.service';
import { CodeRegenerationUIService, CodeRegenerationProgress } from './code-regeneration-ui.service';
import { StepperStateService } from './stepper-state.service';
import { FileModel } from '../components/code-viewer/code-viewer.component';

/**
 * Regeneration Integration Service
 * 
 * This service coordinates the perfect integration between all regeneration-related components:
 * - Sequential Regeneration Service (handles SSE events and file extraction)
 * - Code Regeneration UI Service (handles UI state management)
 * - Code Window Component (handles file updates and accordion creation)
 * - Chat Window Component (handles loading indicators and user interaction)
 * - Code Viewer Component (handles file override and display)
 * - Generation Accordion Component (handles version tracking)
 * - Vertical Stepper Component (isolated during regeneration)
 * 
 * Key Features:
 * - Processes "code-regen" SSE events with progress: "CODE_GENERATION", status: "COMPLETED"
 * - Extracts files from metadata exactly like PAGES_GENERATED step
 * - Overrides previous codes and file models (complete replacement)
 * - Creates new version accordions for each regeneration
 * - Manages UI state transitions (loading → code tab → preview tab)
 * - Isolates stepper during regeneration to prevent interference
 */
@Injectable({
  providedIn: 'root'
})
export class RegenerationIntegrationService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('RegenerationIntegrationService');

  // Core services
  private readonly sequentialRegenerationService = inject(SequentialRegenerationService);
  private readonly codeRegenerationUIService = inject(CodeRegenerationUIService);
  private readonly stepperStateService = inject(StepperStateService);
  private readonly sseDataProcessor = inject(SSEDataProcessorService);
  private readonly enhancedSSEService = inject(EnhancedSSEService);

  // State management
  private readonly regenerationActiveSubject = new BehaviorSubject<boolean>(false);
  private readonly currentVersionSubject = new BehaviorSubject<number>(1);
  private readonly latestFilesSubject = new BehaviorSubject<FileModel[]>([]);

  // Event streams
  private readonly fileUpdateSubject = new Subject<{
    files: FileModel[];
    version: number;
    replaceAll: boolean;
  }>();
  private readonly accordionCreateSubject = new Subject<{
    files: string[];
    version: number;
    projectName: string;
    timestamp: Date;
  }>();
  private readonly uiStateUpdateSubject = new Subject<{
    phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY';
    status: 'IN_PROGRESS' | 'COMPLETED';
    shouldSwitchTab?: 'code' | 'preview';
    shouldRefresh?: boolean;
  }>();

  // Public observables
  readonly regenerationActive$ = this.regenerationActiveSubject.asObservable();
  readonly currentVersion$ = this.currentVersionSubject.asObservable();
  readonly latestFiles$ = this.latestFilesSubject.asObservable();
  readonly fileUpdates$ = this.fileUpdateSubject.asObservable();
  readonly accordionCreate$ = this.accordionCreateSubject.asObservable();
  readonly uiStateUpdates$ = this.uiStateUpdateSubject.asObservable();

  // Subscription management
  private subscriptions = new Subscription();

  constructor() {
    this.logger.info('🔧 Regeneration Integration Service initialized');
    this.setupIntegrations();
  }

  /**
   * Setup integrations between all regeneration services
   * ENHANCED: Direct SSE integration for real-time code-regen handling
   */
  private setupIntegrations(): void {
    this.logger.info('🔗 Setting up regeneration service integrations with direct SSE integration');

    // ENHANCED: Subscribe directly to SSE data processor for code-regen events
    // This provides real-time handling of code-regen SSE events
    this.subscriptions.add(
      this.sseDataProcessor.processedResponse$
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          filter(response => {
            // Only process responses that come from code-regen events
            // Check if this response has the SSE metadata indicating it's from code-regen
            const isCodeRegen = response?.metadata?.some((item: any) =>
              item.type === 'artifact' &&
              item.data?.data?.eventType === 'code-regen'
            );
            this.logger.debug('🔍 Filtering SSE response:', {
              progress: response?.progress,
              status: response?.status,
              isCodeRegen,
              metadataCount: response?.metadata?.length || 0,
              metadata: response?.metadata?.map((m: any) => ({ type: m.type, hasData: !!m.data }))
            });
            return isCodeRegen;
          }),
          tap(response => this.logger.info('📨 Processing code-regen SSE response:', {
            progress: response.progress,
            status: response.status,
            hasMetadata: !!response.metadata?.length
          }))
        )
        .subscribe({
          next: (response) => {
            this.processCodeRegenerationSSEResponse(response);
          },
          error: (error) => {
            this.logger.error('❌ Error in code-regen SSE subscription:', error);
          }
        })
    );

    // FALLBACK: Keep sequential regeneration service as backup
    // This handles code-regen events if SSE data processor doesn't catch them
    this.subscriptions.add(
      this.sequentialRegenerationService.progressUpdates$
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          filter(update => update.event === 'code-regen'), // Only process code-regen events
          tap(update => this.logger.info('📨 FALLBACK: Processing code-regen progress update:', {
            progress: update.progress,
            status: update.status,
            hasMetadata: !!update.metadata,
            deploymentCompleted: update.deploymentCompleted
          }))
        )
        .subscribe(update => {
          this.processRegenerationProgress(update);
        })
    );

    // CRITICAL: Subscribe to sequential regeneration file updates
    // This handles files extracted from CODE_GENERATION COMPLETED events
    this.subscriptions.add(
      this.sequentialRegenerationService.fileUpdates$
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          filter(() => this.regenerationActiveSubject.value), // Only during active regeneration
          tap(files => this.logger.info('📁 Processing regeneration file updates from SSE:', {
            fileCount: files?.length || 0,
            files: files?.map(f => f.name || f.fileName || 'Unknown') || []
          }))
        )
        .subscribe(files => {
          this.processFileUpdates(files);
        })
    );

    // Setup cleanup
    this.destroyRef.onDestroy(() => {
      this.subscriptions.unsubscribe();
      this.logger.info('🧹 Regeneration Integration Service cleaned up');
    });
  }

  /**
   * Start regeneration process
   */
  startRegeneration(): void {
    this.logger.info('🚀 Starting regeneration process');
    
    // Mark regeneration as active
    this.regenerationActiveSubject.next(true);
    
    // Isolate stepper during regeneration
    this.stepperStateService.setRegenerationActive(true);
    
    this.logger.info('🔒 Regeneration started - stepper isolated');
  }

  /**
   * Complete regeneration process
   */
  completeRegeneration(): void {
    this.logger.info('✅ Completing regeneration process');
    
    // Mark regeneration as inactive
    this.regenerationActiveSubject.next(false);
    
    // Re-enable stepper
    this.stepperStateService.setRegenerationActive(false);
    
    this.logger.info('🔓 Regeneration completed - stepper re-enabled');
  }

  /**
   * Process regeneration progress updates from SSE events
   * CRITICAL: Based on actual console SSE data structure
   * - CODE_GENERATION COMPLETED contains files in metadata
   * - DEPLOY COMPLETED triggers tab switch and iframe refresh
   */
  private processRegenerationProgress(update: any): void {
    this.logger.info('🔄 Processing code-regen progress based on console SSE structure:', {
      progress: update.progress,
      status: update.status,
      event: update.event,
      hasMetadata: !!update.metadata,
      deploymentCompleted: update.deploymentCompleted
    });

    // CRITICAL: Handle CODE_GENERATION COMPLETED with files
    if (update.progress === 'CODE_GENERATION' && update.status === 'COMPLETED') {
      this.logger.info('📁 CODE_GENERATION COMPLETED - processing files from metadata');

      // Extract files from metadata exactly like console SSE structure
      if (update.metadata && Array.isArray(update.metadata)) {
        const filesMetadata = update.metadata.find((item: any) => item.type === 'files');
        if (filesMetadata && filesMetadata.data && Array.isArray(filesMetadata.data)) {
          this.logger.info('✅ Found files in CODE_GENERATION COMPLETED metadata:', {
            fileCount: filesMetadata.data.length,
            files: filesMetadata.data.map((f: any) => f.fileName || 'Unknown')
          });

          // Process files directly (they will be handled by file updates subscription)
        }
      }
    }

    // Emit UI state update based on console SSE flow
    this.uiStateUpdateSubject.next({
      phase: update.progress,
      status: update.status,
      shouldSwitchTab: this.determineSwitchTab(update.progress, update.status),
      shouldRefresh: update.progress === 'DEPLOY' && update.status === 'COMPLETED'
    });

    // Handle completion based on console SSE structure
    if (update.deploymentCompleted || (update.progress === 'DEPLOY' && update.status === 'COMPLETED')) {
      this.logger.info('🎉 Regeneration deployment completed');
      this.completeRegeneration();
    }
  }

  /**
   * Process file updates from regeneration
   * CRITICAL: Based on actual console SSE data structure
   * - Files come from CODE_GENERATION COMPLETED events
   * - Complete replacement for regeneration (not append)
   */
  private processFileUpdates(files: FileModel[]): void {
    if (!files || files.length === 0) {
      this.logger.warn('⚠️ No files received from CODE_GENERATION COMPLETED');
      return;
    }

    this.logger.info('📁 Processing file updates from CODE_GENERATION COMPLETED:', {
      fileCount: files.length,
      files: files.map(f => f.name || f.fileName || 'Unknown'),
      source: 'code-regen SSE event'
    });

    // Increment version
    const newVersion = this.currentVersionSubject.value + 1;
    this.currentVersionSubject.next(newVersion);

    // Update latest files
    this.latestFilesSubject.next(files);

    // CRITICAL: Emit file update event with complete replacement flag for regeneration
    this.fileUpdateSubject.next({
      files,
      version: newVersion,
      replaceAll: true // Critical: Complete replacement for regeneration (not append)
    });

    // CRITICAL: Create accordion for regeneration with proper version tracking
    this.createRegenerationAccordion(files, newVersion);

    this.logger.info('✅ File updates processed for regeneration from SSE:', {
      version: newVersion,
      fileCount: files.length,
      replaceAll: true,
      source: 'CODE_GENERATION COMPLETED'
    });
  }

  /**
   * Handle code viewer updates
   */
  private handleCodeViewerUpdate(files: any[]): void {
    this.logger.info('🖥️ Handling code viewer update:', { fileCount: files?.length || 0 });
    
    // Convert to FileModel format if needed
    const fileModels = this.convertToFileModels(files);
    this.processFileUpdates(fileModels);
  }

  /**
   * Handle accordion updates
   */
  private handleAccordionUpdate(accordionData: any): void {
    this.logger.info('📋 Handling accordion update:', accordionData);

    if (accordionData.files && accordionData.files.length > 0) {
      // Extract file names
      const fileNames = accordionData.files.map((file: any) => 
        file.fileName || file.name || file.path || 'Unknown file'
      );

      // Emit accordion creation event
      this.accordionCreateSubject.next({
        files: fileNames,
        version: accordionData.version,
        projectName: 'Regenerated Project', // Could be made configurable
        timestamp: new Date()
      });
    }
  }

  /**
   * Convert various file formats to FileModel
   */
  private convertToFileModels(files: any[]): FileModel[] {
    if (!files || !Array.isArray(files)) {
      return [];
    }

    return files.map(file => ({
      name: file.fileName || file.name || file.path || 'Unknown file',
      fileName: file.fileName || file.name || file.path,
      type: 'file' as const,
      content: file.content || file.code || file.data || '',
    }));
  }

  /**
   * Create regeneration accordion with proper version tracking
   * CRITICAL: Based on actual console SSE data structure
   */
  private createRegenerationAccordion(files: FileModel[], version: number): void {
    this.logger.info('📋 Creating regeneration accordion from CODE_GENERATION COMPLETED:', {
      fileCount: files.length,
      version: version,
      files: files.map(f => f.name || f.fileName || 'Unknown')
    });

    // Create accordion data exactly like console SSE structure
    const accordionInfo = {
      files: files.map(f => f.name || f.fileName || 'Unknown'),
      version: version,
      projectName: 'Regenerated Project',
      timestamp: new Date()
    };

    // Emit accordion creation event
    this.accordionCreateSubject.next(accordionInfo);

    this.logger.info('✅ Regeneration accordion created:', accordionInfo);
  }

  /**
   * Determine which tab to switch to based on progress and status
   */
  private determineSwitchTab(progress: string, status: string): 'code' | 'preview' | undefined {
    if (progress === 'CODE_GENERATION' && status === 'COMPLETED') {
      return 'code'; // Switch to code tab to show new files
    }
    if (progress === 'DEPLOY' && status === 'COMPLETED') {
      return 'preview'; // Switch to preview tab to show deployed app
    }
    return undefined;
  }

  /**
   * Get current regeneration state
   */
  isRegenerationActive(): boolean {
    return this.regenerationActiveSubject.value;
  }

  /**
   * Get current version
   */
  getCurrentVersion(): number {
    return this.currentVersionSubject.value;
  }

  /**
   * Get latest files
   */
  getLatestFiles(): FileModel[] {
    return this.latestFilesSubject.value;
  }
}
