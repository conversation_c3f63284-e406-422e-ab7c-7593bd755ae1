import { Injectable, inject } from '@angular/core';
import { createLogger } from '../utils/logger';
import { EnhancedSSEService } from './enhanced-sse.service';
import { SSEDataProcessorService } from './sse-data-processor.service';
import { RegenerationIntegrationService } from './regeneration-integration.service';

/**
 * Service to fix and monitor code-regen SSE connection issues
 * This service ensures continuous SSE listening across initial-code-gen and code-regen events
 */
@Injectable({
  providedIn: 'root'
})
export class CodeRegenConnectionFixService {
  private readonly logger = createLogger('CodeRegenConnectionFixService');
  private readonly enhancedSSE = inject(EnhancedSSEService);
  private readonly sseDataProcessor = inject(SSEDataProcessorService);
  private readonly regenerationIntegration = inject(RegenerationIntegrationService);

  /**
   * Diagnose current SSE connection status and identify issues
   */
  diagnoseConnectionStatus(): {
    status: 'healthy' | 'issues' | 'broken';
    issues: string[];
    recommendations: string[];
    connectionDetails: any;
  } {
    this.logger.info('🔍 Diagnosing SSE connection status for code-regen');

    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check Enhanced SSE Service status
    const enhancedStatus = this.enhancedSSE.getServiceStatus();
    const connectionStats = this.enhancedSSE.getConnectionStatistics();
    const monitoringDetails = this.enhancedSSE.getCurrentMonitoringDetails();

    this.logger.info('📊 Enhanced SSE Status:', enhancedStatus);
    this.logger.info('📊 Connection Statistics:', connectionStats);
    this.logger.info('📊 Monitoring Details:', monitoringDetails);

    // Check if connection is active
    if (!enhancedStatus.enhancedSSE.isConnected) {
      issues.push('Enhanced SSE service is not connected');
      recommendations.push('Restart SSE monitoring with current project/job ID');
    }

    // Check if there are active connections
    if (connectionStats.activeConnections === 0) {
      issues.push('No active SSE connections found');
      recommendations.push('Establish new SSE connection for current project');
    }

    // Check if multiple connections exist (potential duplicate issue)
    if (connectionStats.activeConnections > 1) {
      issues.push(`Multiple active connections detected (${connectionStats.activeConnections})`);
      recommendations.push('Clean up duplicate connections');
    }

    // Check if monitoring details are valid
    if (!monitoringDetails.projectId || !monitoringDetails.jobId) {
      issues.push('Missing project ID or job ID in monitoring details');
      recommendations.push('Ensure proper project/job ID tracking');
    }

    // Check SSE data processor status
    const dataProcessorStatus = this.sseDataProcessor.getCurrentState();
    this.logger.info('📊 SSE Data Processor Status:', dataProcessorStatus);

    // Check regeneration integration status
    const regenerationStatus = this.regenerationIntegration.getCurrentState();
    this.logger.info('📊 Regeneration Integration Status:', regenerationStatus);

    const status = issues.length === 0 ? 'healthy' : issues.length <= 2 ? 'issues' : 'broken';

    return {
      status,
      issues,
      recommendations,
      connectionDetails: {
        enhancedSSE: enhancedStatus,
        connectionStats,
        monitoringDetails,
        dataProcessorStatus,
        regenerationStatus
      }
    };
  }

  /**
   * Fix common SSE connection issues for code-regen
   */
  fixConnectionIssues(projectId: string, jobId: string): void {
    this.logger.info('🔧 Fixing SSE connection issues for code-regen', { projectId, jobId });

    // Step 1: Clean up any existing connections
    this.logger.info('🧹 Step 1: Cleaning up existing connections');
    this.enhancedSSE.prepareForRegeneration(projectId, jobId);

    // Step 2: Ensure SSE data processor is ready
    this.logger.info('🔄 Step 2: Resetting SSE data processor');
    // The data processor should be stateless, but we can log its current state
    const currentState = this.sseDataProcessor.getCurrentState();
    this.logger.info('📊 Current SSE data processor state:', currentState);

    // Step 3: Restart SSE monitoring with proper configuration
    this.logger.info('🚀 Step 3: Restarting SSE monitoring');
    this.enhancedSSE.startMonitoring(projectId, jobId, {
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      enableExponentialBackoff: true,
      enableHeartbeat: true,
      heartbeatInterval: 30000
    });

    // Step 4: Verify connection is established
    setTimeout(() => {
      const diagnosis = this.diagnoseConnectionStatus();
      this.logger.info('✅ Connection fix completed. Status:', diagnosis.status);
      if (diagnosis.issues.length > 0) {
        this.logger.warn('⚠️ Some issues remain:', diagnosis.issues);
      }
    }, 2000);
  }

  /**
   * Monitor SSE events in real-time for debugging
   */
  startEventMonitoring(): void {
    this.logger.info('👁️ Starting real-time SSE event monitoring');

    // Monitor processed responses from SSE data processor
    this.sseDataProcessor.processedResponse$.subscribe({
      next: (response) => {
        this.logger.info('📨 SSE Processed Response:', {
          progress: response.progress,
          status: response.status,
          hasMetadata: !!response.metadata?.length,
          metadataTypes: response.metadata?.map((m: any) => m.type) || [],
          isCodeRegen: response.metadata?.some((m: any) => 
            m.type === 'artifact' && m.data?.data?.eventType === 'code-regen'
          )
        });
      },
      error: (error) => {
        this.logger.error('❌ SSE Processed Response Error:', error);
      }
    });

    // Monitor regeneration integration UI state updates
    this.regenerationIntegration.uiStateUpdates$.subscribe({
      next: (update) => {
        this.logger.info('🎛️ Regeneration UI State Update:', update);
      },
      error: (error) => {
        this.logger.error('❌ Regeneration UI State Error:', error);
      }
    });

    this.logger.info('✅ Event monitoring started - check console for real-time updates');
  }

  /**
   * Test code-regen event processing with mock data
   */
  testCodeRegenEventProcessing(): void {
    this.logger.info('🧪 Testing code-regen event processing with mock data');

    // Simulate a code-regen SSE event
    const mockSSEEvent = {
      id: `test-code-regen-${Date.now()}`,
      event: 'code-regen',
      data: JSON.stringify({
        status: 'COMPLETED',
        progress: 'CODE_GENERATION',
        log: 'Test code regeneration completed',
        progress_description: 'Testing code-regen event processing',
        metadata: [
          {
            type: 'files',
            data: [
              {
                fileName: 'test-regen.js',
                content: 'console.log("Test regenerated file");'
              }
            ]
          }
        ]
      })
    };

    // Process through SSE data processor
    this.sseDataProcessor.processSSEEvent(mockSSEEvent).subscribe({
      next: (response) => {
        this.logger.info('✅ Mock code-regen event processed successfully:', response);
      },
      error: (error) => {
        this.logger.error('❌ Mock code-regen event processing failed:', error);
      }
    });
  }

  /**
   * Force reconnection for code-regen events
   */
  forceReconnection(projectId: string, jobId: string): void {
    this.logger.info('🔌 Forcing SSE reconnection for code-regen', { projectId, jobId });

    // Stop current monitoring
    this.enhancedSSE.stopMonitoring();

    // Wait a moment for cleanup
    setTimeout(() => {
      // Restart monitoring
      this.enhancedSSE.startMonitoring(projectId, jobId, {
        reconnectInterval: 1000,
        maxReconnectAttempts: 15,
        enableExponentialBackoff: true
      });

      this.logger.info('✅ Forced reconnection initiated');
    }, 1000);
  }

  /**
   * Get comprehensive status report
   */
  getStatusReport(): any {
    const diagnosis = this.diagnoseConnectionStatus();
    const enhancedStatus = this.enhancedSSE.getServiceStatus();
    const connectionStats = this.enhancedSSE.getConnectionStatistics();

    return {
      timestamp: new Date().toISOString(),
      diagnosis,
      enhancedSSE: enhancedStatus,
      connectionStats,
      recommendations: diagnosis.recommendations
    };
  }
}
