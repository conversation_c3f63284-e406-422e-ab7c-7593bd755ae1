<div class="agents-container">
  <!-- Header section -->
  <div class="agents-header">
    <div class="header-content">
      <div class="search-section">
        <app-search-bar></app-search-bar>
      </div>
      <div class="action-buttons">
        <button class="action-button">{{ agentsConfig.labels.import }} <img src="{{ agentsConfig.icons.import }}" alt="Import"></button>
        <button class="action-button">{{ agentsConfig.labels.export }} <img src="{{ agentsConfig.icons.export }}" alt="Export"></button>
        <button class="action-button" [class.active-filter]="isFilterBarVisible" (click)="toggleFilterBar()">
          {{ agentsConfig.labels.filters }}
          <!-- Down arrow when closed, Up arrow when open -->
          <svg *ngIf="!isFilterBarVisible" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <svg *ngIf="isFilterBarVisible" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 15L12 10L17 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Filter Bar -->
  <div class="filter-section" *ngIf="isFilterBarVisible">
    <app-filter-bar 
      [filterConfig]="agentFilterConfig"
      (filterChange)="onFilterChange($event)">
    </app-filter-bar>
  </div>
  
  <div class="cards-container">
    <!-- Create Agent Card - show only on first page -->
    <app-create-card 
      *ngIf="showCreateCard"
      [label]="agentsConfig.labels.createAgent" 
      (cardClick)="onCreateAgent()">
    </app-create-card>
    
    <!-- No results message -->
    <div class="no-results" *ngIf="filteredAgents.length === 0">
      {{ agentsConfig.labels.noResults }}
    </div>
    
    <!-- Agent Data Cards - Now showing paginated cards -->
    <app-data-card 
      *ngFor="let agent of displayedAgents" 
      [data]="agent"
      (cardClicked)="onCardClicked($event)"
      (actionClicked)="onActionClicked($event)">
    </app-data-card>
  </div>
  
  <!-- Page Footer with Pagination - use totalPages from the service -->
  <app-page-footer
    *ngIf="filteredAgents.length > 0"
    [totalItems]="filteredAgents.length + 1" 
    [currentPage]="currentPage"
    [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"
  ></app-page-footer>
</div>